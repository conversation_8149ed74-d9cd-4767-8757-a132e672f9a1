name: Security Scans
run-name: <PERSON> Scans for ${{ inputs.tag-name || github.sha }}

on:
  workflow_call:
    inputs:
      tag-name:
        description: "Docker image tag to scan"
        required: true
        type: string
      gcp-project-id:
        description: "GCP Project ID"
        required: true
        type: string
      gcp-region:
        description: "GCP Region"
        required: true
        type: string
      run-scans:
        description: "Whether to run security scans"
        required: falseAbsolutely! Here’s a checklist and **step-by-step instructions** for a developer to ensure your called workflow (`security-scans.yml`) is implemented correctly and works as expected, especially considering its use as a _reusable workflow_ in GitHub Actions.

---
# ✅ Security Scans Workflow: Developer Instructions

## 1. **Declare All Required Secrets in `workflow_call`**

**You must declare each secret your workflow expects in the `on.workflow_call.secrets` section.**  
Currently, your jobs use `secrets.GCP_SERVICE_ACCOUNT_KEY`.  
**But your posted `on.workflow_call:` block is missing the secret declaration!**

**Add the following:**
```yaml
on:
  workflow_call:
    inputs:
      tag-name:
        ...
    # Add this block:
    secrets:
      GCP_SERVICE_ACCOUNT_KEY:
        required: false
```

Without this, you will get errors like  
> "Unrecognized named-value: 'secrets'..."

## 2. **Usage From Caller Workflow**

When invoking this workflow (as you do in your main workflow), you should:
```yaml
jobs:
  security-scan:
    uses: ./github/workflows/security-scans.yml
    with:
      tag-name: ...
      gcp-project-id: ...
      gcp-region: ...
      ...
    secrets: inherit  # or secrets:
                      #   GCP_SERVICE_ACCOUNT_KEY: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}
```
- You can use **`secrets: inherit`** if you want all available secrets.
- Or, **explicitly pass** just the ones needed.

## 3. **Secret Usage in Steps**

Now, inside your jobs, `${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}` will be available **for use in steps** (including in `if` conditions).

## 4. **Review All Secret Usages**
- The only secret you reference is `GCP_SERVICE_ACCOUNT_KEY`. If you ever add more, **declare each one in `workflow_call.secrets`**.
- **You cannot use secrets in the `env:` block (outside of steps)!** But your usage is correct—all secret references are within steps.

## 5. **Verify Step Logic**

- Your step `if:` and secret access now works:
  - `if: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY != '' }}`  
    (Valid as long as secret is declared and passed)
- Add a **guard step** (as you did for "Skip Authentication (Debug)")—good for debugging and failing early if secret missing.

## 6. **Best Practices & Linting**
- Make sure the file is valid YAML and formatted correctly (`.yml`, spaces not tabs, etc).
- Commit and push this change—no need to bump workflow version unless using a remote repo, this applies to local repos as well.

## 7. **Optional: Add Docs in Workflow Header**
Add a comment describing inputs and secrets at the top—for maintenance:
```yaml
# This workflow is intended for use as a reusable workflow (workflow_call).
# Inputs:
#   - tag-name (string, required): Docker image tag
#   ...
# Secrets:
#   - GCP_SERVICE_ACCOUNT_KEY: GCP service account credentials.
```

## 8. **Sample Patch**

Here’s a minimal diff for your workflow:

```diff
 on:
   workflow_call:
     inputs:
       tag-name:
         description: "Docker image tag to scan"
         required: true
         type: string
       gcp-project-id:
         description: "GCP Project ID"
         required: true
         type: string
       gcp-region:
         description: "GCP Region"
         required: true
         type: string
       run-scans:
         description: "Whether to run security scans"
         required: false
         type: boolean
         default: true
       environment:
         description: "GitHub environment to use"
         required: false
         type: string
         default: "development"
+    secrets:
+      GCP_SERVICE_ACCOUNT_KEY:
+        required: false
```

## 9. **Test It**

- **After this change**, commit, push, and observe your workflow run.
- If you see lines like  
  > "GCP_SERVICE_ACCOUNT_KEY exists: true"  
  in the workflow output, you're in business!

# 🟢 Summary: Most Important Steps

1. **Declare all used secrets in `on.workflow_call.secrets` in your called workflow.**
2. **Pass those secrets from the caller, using `secrets: inherit` or explicit mapping.**
3. **Don’t reference secrets anywhere outside steps/step `if:` conditions.**
4. **Test the workflow after changes.**

Let me know if you need sample files or if you get any errors!
        type: boolean
        default: true
      environment:
        description: "GitHub environment to use"
        required: false
        type: string
        default: "development"

jobs:
  security-scan:
    name: "Security Vulnerability Scans"
    runs-on: ubuntu-latest
    if: inputs.run-scans == true
    environment: ${{ inputs.environment }}
    env:
      TAG_NAME: ${{ inputs.tag-name }}
      GCP_PROJECT_ID: ${{ inputs.gcp-project-id }}
      GCP_REGION: ${{ inputs.gcp-region }}
    
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Debug Environment Variables
        run: |
          echo "=== Environment Variables ==="
          echo "TAG_NAME: ${{ env.TAG_NAME }}"
          echo "GCP_PROJECT_ID: ${{ env.GCP_PROJECT_ID }}"
          echo "GCP_REGION: ${{ env.GCP_REGION }}"
          echo ""
          echo "=== Inputs ==="
          echo "tag-name: '${{ inputs.tag-name }}'"
          echo "gcp-project-id: '${{ inputs.gcp-project-id }}'"
          echo "gcp-region: '${{ inputs.gcp-region }}'"
          echo "run-scans: ${{ inputs.run-scans }}"
          echo ""
          echo "=== Secrets ==="
          echo "GCP_SERVICE_ACCOUNT_KEY exists: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY != '' }}"
          echo "GCP_SERVICE_ACCOUNT_KEY length: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY != '' && 'present' || 'missing' }}"

      - name: Authenticate with Google Cloud
        if: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY != '' }}
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Skip Authentication (Debug)
        if: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY == '' }}
        run: |
          echo "❌ GCP_SERVICE_ACCOUNT_KEY secret is not available"
          echo "Available secrets (names only):"
          env | grep -E '^[A-Z_]+=' | cut -d= -f1 | sort
          exit 1

      - name: Configure Docker
        run: gcloud auth configure-docker "${{ env.GCP_REGION }}-docker.pkg.dev"

      - name: Dependency Security Scan
        uses: aquasecurity/trivy-action@0.32.0
        continue-on-error: ${{ contains(github.event.pull_request.labels.*.name, 'bypass-vulnerability-checks') }}
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'table'
          scanners: 'vuln,secret'
          severity: 'CRITICAL'
          exit-code: 1
          timeout: '10m'

      - name: Dependency Security Scan - JSON Report
        uses: aquasecurity/trivy-action@0.32.0
        continue-on-error: true
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'json'
          output: 'security-report.json'
          scanners: 'vuln,secret'
          severity: 'CRITICAL,HIGH,MEDIUM,LOW'
          exit-code: 0
          timeout: '10m'

      - name: Cacao API Container Security Scan
        uses: aquasecurity/trivy-action@0.32.0
        continue-on-error: ${{ contains(github.event.pull_request.labels.*.name, 'bypass-vulnerability-checks') }}
        with:
          image-ref: '${{ env.GCP_REGION }}-docker.pkg.dev/${{ env.GCP_PROJECT_ID }}/palmyra-pro-images/cacao-api:${{ env.TAG_NAME }}'
          format: 'table'
          scanners: 'vuln,secret'
          severity: 'CRITICAL'
          exit-code: 1
          timeout: '10m'

      - name: Cacao API Container Security Scan - JSON Report
        uses: aquasecurity/trivy-action@0.32.0
        continue-on-error: true
        with:
          image-ref: '${{ env.GCP_REGION }}-docker.pkg.dev/${{ env.GCP_PROJECT_ID }}/palmyra-pro-images/cacao-api:${{ env.TAG_NAME }}'
          format: 'json'
          output: 'cacao-api-container-report.json'
          scanners: 'vuln,secret'
          severity: 'CRITICAL,HIGH,MEDIUM,LOW'
          exit-code: 0
          timeout: '10m'

      - name: Upload Security Reports
        uses: actions/upload-artifact@v4
        with:
          name: security-reports-${{ env.TAG_NAME }}
          path: |
            security-report.json
            cacao-api-container-report.json
          retention-days: 30

      - name: Security Scan Summary
        run: |
          echo "🔒 Security scans completed for tag: ${{ env.TAG_NAME }}"
          echo "Reports uploaded as artifacts for detailed analysis"
