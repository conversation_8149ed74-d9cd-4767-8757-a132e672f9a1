name: Create or Update an environment
run-name: >
  Create or Update ${{ inputs.shortName != '' && inputs.shortName || format('pr-{0}-{1}', github.event.pull_request.number, github.event.pull_request.user.login) }}

on:
  pull_request:
    types: [opened, reopened, edited]
  workflow_dispatch:
    inputs:
      environment:
        type: environment
        description: "Environment"
        required: true
        default: "development"
      shortName:
        type: string
        description: "staging, prod or any other short name used as prefix for resources, labels, etc."
        required: true
        default: "PR"
      seedDev:
        type: boolean
        description: "Seed database with dev data"
        required: true
        default: true
      runSecurityScans:
        type: boolean
        description: "Run security scans (Trivy)"
        required: false
        default: true

jobs:
  setup:
    name: "Setup & Validation"
    runs-on: ubuntu-latest
    environment: ${{ inputs.environment || 'development' }}
    outputs:
      short-name: ${{ steps.set-vars.outputs.short-name }}
      tag-name: ${{ steps.set-vars.outputs.tag-name }}
      channel-id: ${{ steps.set-vars.outputs.channel-id }}
      fe-url: ${{ steps.set-vars.outputs.fe-url }}
      api-url: ${{ steps.set-vars.outputs.api-url }}
      db-name: ${{ steps.set-vars.outputs.db-name }}
      db-host: ${{ steps.set-vars.outputs.db-host }}
      min-instances: ${{ steps.set-vars.outputs.min-instances }}
      gcp-project-number: ${{ steps.set-vars.outputs.gcp-project-number }}
      gcp-db-password-secret-name: ${{ steps.set-vars.outputs.gcp-db-password-secret-name }}
      db-instance-connection-name: ${{ steps.set-vars.outputs.db-instance-connection-name }}
    env:
      DB_INSTANCE_CONNECTION_NAME: "${{ vars.GCP_PROJECT_ID }}:${{ vars.GCP_REGION }}:${{ vars.DB_INSTANCE }}"
    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Set dynamic variables
        id: set-vars
        run: |
          # If inputs.shortName is not present, it means its a PR.
          INPUTS_SHORT_NAME="${{ inputs.shortName || 'PR' }}"

          if [[ "${INPUTS_SHORT_NAME}" == "prod" ]]; then
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="live"
            TAG_NAME="latest"
            FE_URL="https://cacao.palmyra.pro"

            # To avoid mistakes, let's don't allow prod deployment into development.
            if [[ "${{ inputs.environment || 'development' }}" != "production" ]]; then
              echo "❌ Production deployment not allowed in development environment"
              exit 1
            fi
          elif [[ "${INPUTS_SHORT_NAME}" == "staging" ]]; then
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="live"
            TAG_NAME="staging-latest"
            FE_URL="https://cacao---dev.web.app"
          elif [[ "${INPUTS_SHORT_NAME}" == "PR" ]]; then
            RAW_NAME="pr-${{ github.event.pull_request.number }}-${{ github.event.pull_request.user.login }}"
            SHORT_NAME=$(echo "${RAW_NAME}" | tr '[:upper:]' '[:lower:]' | tr -c 'a-z0-9-' '-' | cut -c1-40)
            CHANNEL_ID="${SHORT_NAME}"
            TAG_NAME="${SHORT_NAME}"
            FE_URL=""
          else
            SHORT_NAME="${INPUTS_SHORT_NAME}"
            CHANNEL_ID="${INPUTS_SHORT_NAME}"
            TAG_NAME="${INPUTS_SHORT_NAME}"
            FE_URL=""
          fi

          # Set minimum instances based on environment
          if [[ "$SHORT_NAME" == "staging" ]]; then
            MIN_INSTANCES=1
          else
            MIN_INSTANCES=0
          fi

          # Get GCP project number
          GCP_PROJECT_NUMBER=$(gcloud projects describe ${{ vars.GCP_PROJECT_ID }} --format='value(projectNumber)')
          GCP_DB_PASSWORD_SECRET_NAME="DB_PASSWORD_$(echo "${{ vars.DB_INSTANCE }}" | tr '[:lower:]' '[:upper:]' | tr '-' '_')"

          # Set database variables
          DB_NAME="palmyrapro-db-$SHORT_NAME"
          DB_HOST="/cloudsql/${DB_INSTANCE_CONNECTION_NAME}"
          DB_INSTANCE_CONNECTION_NAME="${{ vars.GCP_PROJECT_ID }}:${{ vars.GCP_REGION }}:${{ vars.DB_INSTANCE }}"

          # Set API URL
          API_URL="https://$SHORT_NAME-cacao-api-${GCP_PROJECT_NUMBER}.${{ vars.GCP_REGION }}.run.app"

          # Output variables for other jobs
          echo "short-name=$SHORT_NAME" >> $GITHUB_OUTPUT
          echo "tag-name=$TAG_NAME" >> $GITHUB_OUTPUT
          echo "channel-id=$CHANNEL_ID" >> $GITHUB_OUTPUT
          echo "fe-url=$FE_URL" >> $GITHUB_OUTPUT
          echo "api-url=$API_URL" >> $GITHUB_OUTPUT
          echo "db-name=$DB_NAME" >> $GITHUB_OUTPUT
          echo "db-host=$DB_HOST" >> $GITHUB_OUTPUT
          echo "min-instances=$MIN_INSTANCES" >> $GITHUB_OUTPUT
          echo "gcp-project-number=$GCP_PROJECT_NUMBER" >> $GITHUB_OUTPUT
          echo "gcp-db-password-secret-name=$GCP_DB_PASSWORD_SECRET_NAME" >> $GITHUB_OUTPUT
          echo "db-instance-connection-name=$DB_INSTANCE_CONNECTION_NAME" >> $GITHUB_OUTPUT

          # Also set as environment variables for this job
          echo "SHORT_NAME=$SHORT_NAME" >> $GITHUB_ENV
          echo "TAG_NAME=$TAG_NAME" >> $GITHUB_ENV
          echo "CHANNEL_ID=$CHANNEL_ID" >> $GITHUB_ENV
          echo "FE_URL=$FE_URL" >> $GITHUB_ENV
          echo "API_URL=$API_URL" >> $GITHUB_ENV
          echo "DB_NAME=$DB_NAME" >> $GITHUB_ENV
          echo "DB_HOST=$DB_HOST" >> $GITHUB_ENV
          echo "MIN_INSTANCES=$MIN_INSTANCES" >> $GITHUB_ENV
          echo "GCP_PROJECT_NUMBER=$GCP_PROJECT_NUMBER" >> $GITHUB_ENV
          echo "GCP_DB_PASSWORD_SECRET_NAME=$GCP_DB_PASSWORD_SECRET_NAME" >> $GITHUB_ENV

      - name: Validate configuration
        run: |
          echo "✅ Configuration validated:"
          echo "  Environment: ${{ inputs.environment || 'development' }}"
          echo "  Short Name: ${{ env.SHORT_NAME }}"
          echo "  Tag Name: ${{ env.TAG_NAME }}"
          echo "  Channel ID: ${{ env.CHANNEL_ID }}"
          echo "  Database: ${{ env.DB_NAME }}"
          echo "  API URL: ${{ env.API_URL }}"
          echo "  Frontend URL: ${{ env.FE_URL }}"

  database:
    name: "Database Operations"
    runs-on: ubuntu-latest
    needs: setup
    environment: ${{ inputs.environment || 'development' }}
    env:
      SHORT_NAME: ${{ needs.setup.outputs.short-name }}
      DB_NAME: ${{ needs.setup.outputs.db-name }}
      DB_HOST: ${{ needs.setup.outputs.db-host }}
      DB_INSTANCE_CONNECTION_NAME: ${{ needs.setup.outputs.db-instance-connection-name }}
      DB_PASSWORD: ${{ secrets.DB_PASSWORD }}
      DB_USER: ${{ vars.DB_USER }}
      DB_PORT: ${{ vars.DB_PORT }}

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Install Cloud SQL Auth Proxy
        run: |
          curl -o cloud-sql-proxy https://storage.googleapis.com/cloud-sql-connectors/cloud-sql-proxy/v2.15.2/cloud-sql-proxy.linux.amd64
          chmod +x cloud-sql-proxy
          sudo mv cloud-sql-proxy /usr/local/bin/

      - name: Start Cloud SQL Proxy
        run: |
          cloud-sql-proxy $DB_INSTANCE_CONNECTION_NAME --credentials-file=$GOOGLE_APPLICATION_CREDENTIALS --port 5432 &

          # Wait and check if port is open
          for i in {1..10}; do
            echo "Checking if proxy is up (attempt $i)..."
            nc -z 127.0.0.1 5432 && break
            sleep 2
          done

          # Final check
          if ! nc -z 127.0.0.1 5432; then
            echo "❌ Cloud SQL Proxy failed to start."
            exit 1
          fi

          echo "✅ Cloud SQL Proxy is up and running."

      - name: Check if database exists
        id: check-db
        env:
          PGPASSWORD: ${{ env.DB_PASSWORD }}
        run: |
          DB_EXISTS=$(psql -h 127.0.0.1 -U $DB_USER -d postgres \
            -tAc "SELECT 1 FROM pg_database WHERE datname = '$DB_NAME'" | grep -q 1 && echo true || echo false)

          echo "exists=$DB_EXISTS" >> $GITHUB_OUTPUT
          echo "Database '$DB_NAME' exists: $DB_EXISTS"

      - name: Create Cloud SQL schema
        if: steps.check-db.outputs.exists == 'false'
        run: |
          echo "Creating database: $DB_NAME"
          gcloud sql databases create $DB_NAME --instance=${{ vars.DB_INSTANCE }}

      - name: Run DB migrations / init scripts
        if: steps.check-db.outputs.exists == 'false'
        env:
          PGPASSWORD: ${{ env.DB_PASSWORD }}
        run: |
          echo "Running database migrations for: $DB_NAME"
          psql "host=127.0.0.1 port=5432 user=$DB_USER dbname=$DB_NAME" -f "packages/db/schema/01-ddl.sql"

          # Run seeding based on seedDev input
          if [[ "${{ inputs.seedDev }}" == "true" ]]; then
            echo "Seeding database with development data..."
            psql "host=127.0.0.1 port=5432 user=$DB_USER dbname=$DB_NAME" -f "packages/db/schema/02-seeding.sql"
          else
            echo "Skipping database seeding (seedDev=false)"
          fi

          echo "✅ Database migrations completed"

  build:
    name: "Build Applications"
    runs-on: ubuntu-latest
    needs: setup
    environment: ${{ inputs.environment || 'development' }}
    env:
      SHORT_NAME: ${{ needs.setup.outputs.short-name }}
      TAG_NAME: ${{ needs.setup.outputs.tag-name }}
      API_URL: ${{ needs.setup.outputs.api-url }}

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Use Node.js from .nvmrc
        uses: actions/setup-node@v4
        with:
          node-version-file: ".nvmrc"
          cache: "npm"

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Configure Docker
        run: gcloud auth configure-docker "${{ vars.GCP_REGION }}-docker.pkg.dev"

      - name: Build Frontend
        env:
          NEXT_PUBLIC_BACKEND_URL: "${{ env.API_URL }}"
          CI: "true"
          SKIP_TYPE_CHECK: "true"
          # Firebase configuration (mix of variables and secrets)
          NEXT_PUBLIC_FIREBASE_API_KEY: "${{ secrets.NEXT_PUBLIC_FIREBASE_API_KEY }}"
          NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: "${{ vars.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN }}"
          NEXT_PUBLIC_FIREBASE_PROJECT_ID: "${{ vars.NEXT_PUBLIC_FIREBASE_PROJECT_ID }}"
          NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: "${{ vars.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET }}"
          NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: "${{ vars.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID }}"
          NEXT_PUBLIC_FIREBASE_APP_ID: "${{ secrets.NEXT_PUBLIC_FIREBASE_APP_ID }}"
        run: |
          echo "Building frontend application..."
          npm ci
          npm run generate --workspace=packages/api-specs
          npm run build --workspace=apps/frontend
          du -sh apps/frontend/dist/.
          echo "✅ Frontend build completed"

      - name: Build and Push Docker Image
        run: |
          echo "Building backend Docker image..."
          docker build -t ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/cacao-api:$TAG_NAME -f ./apps/api/Dockerfile .
          docker push ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/cacao-api:$TAG_NAME
          echo "✅ Backend Docker image built and pushed"

      - name: Upload Frontend Build Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: frontend-build
          path: apps/frontend/dist/
          retention-days: 1

  security-scan:
    name: "Security Scans"
    if: github.event_name == 'pull_request' || inputs.runSecurityScans == true
    needs: [setup, build]
    uses: ./.github/workflows/security-scans.yml
    with:
      tag-name: ${{ needs.setup.outputs.tag-name }}
      gcp-project-id: ${{ vars.GCP_PROJECT_ID }}
      gcp-region: ${{ vars.GCP_REGION }}
      run-scans: true
    secrets:
      gcp-service-account-key: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

  deploy:
    name: "Deploy Applications"
    runs-on: ubuntu-latest
    needs: [setup, database, build, security-scan]
    if: always() && needs.setup.result == 'success' && needs.database.result == 'success' && needs.build.result == 'success' && (needs.security-scan.result == 'success' || needs.security-scan.result == 'skipped')
    environment: ${{ inputs.environment || 'development' }}
    env:
      SHORT_NAME: ${{ needs.setup.outputs.short-name }}
      TAG_NAME: ${{ needs.setup.outputs.tag-name }}
      CHANNEL_ID: ${{ needs.setup.outputs.channel-id }}
      FE_URL: ${{ needs.setup.outputs.fe-url }}
      API_URL: ${{ needs.setup.outputs.api-url }}
      DB_NAME: ${{ needs.setup.outputs.db-name }}
      DB_HOST: ${{ needs.setup.outputs.db-host }}
      MIN_INSTANCES: ${{ needs.setup.outputs.min-instances }}
      GCP_DB_PASSWORD_SECRET_NAME: ${{ needs.setup.outputs.gcp-db-password-secret-name }}
      DB_INSTANCE_CONNECTION_NAME: ${{ needs.setup.outputs.db-instance-connection-name }}

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Authenticate with Google Cloud
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}

      - name: Download Frontend Build Artifacts
        uses: actions/download-artifact@v4
        with:
          name: frontend-build
          path: apps/frontend/dist/

      - name: Deploy to Firebase Hosting
        id: deploy-frontend
        working-directory: apps/frontend
        env:
          NODE_OPTIONS: "--trace-deprecation"
        run: |
          npm install -g firebase-tools

          # Deploy
          TMP_OUTPUT=$(mktemp)

          EXIT_CODE=0
          if [ "$CHANNEL_ID" == "live" ]; then
            echo "Deploying to live channel..."
            firebase deploy \
            --only hosting \
            --project="${{ vars.GCP_PROJECT_ID }}" \
            --json >"$TMP_OUTPUT" 2>&1 || EXIT_CODE=$?

            if [ $EXIT_CODE -ne 0 ]; then
              echo "❌ Firebase deploy failed:"
              cat "$TMP_OUTPUT"
              rm "$TMP_OUTPUT"
              exit $EXIT_CODE
            fi

            # Extract and comment the PREVIEW URL
            TMP_PREVIEW_URL="https://${{ vars.GCP_PROJECT_ID }}.web.app/"

            echo "✅ Frontend deployed to: $TMP_PREVIEW_URL"
            echo "PREVIEW_URL=$TMP_PREVIEW_URL" >> "$GITHUB_OUTPUT"

          else
            echo "Deploying to preview channel: $CHANNEL_ID"
            firebase hosting:channel:deploy "$CHANNEL_ID" \
              --project="${{ vars.GCP_PROJECT_ID }}" \
              --json >"$TMP_OUTPUT" 2>&1 || EXIT_CODE=$?

            if [ $EXIT_CODE -ne 0 ]; then
              echo "❌ Firebase deploy failed:"
              cat "$TMP_OUTPUT"
              rm "$TMP_OUTPUT"
              exit $EXIT_CODE
            fi

            # Extract and comment the PREVIEW URL
            TMP_PREVIEW_URL=$(jq -r '.result[].url' ${TMP_OUTPUT})
            echo "✅ Frontend deployed to: $TMP_PREVIEW_URL"
            echo "PREVIEW_URL=$TMP_PREVIEW_URL" >> "$GITHUB_OUTPUT"

            echo "FE_URL=${TMP_PREVIEW_URL}" >> $GITHUB_ENV
          fi

          rm "$TMP_OUTPUT"

      - name: Deploy to Cloud Run
        id: deploy-backend
        run: |
          echo "Deploying backend to Cloud Run..."

          # Use the frontend URL from the previous step if it was updated
          ALLOWED_ORIGIN="${{ needs.setup.outputs.fe-url }}"
          if [ -n "${{ steps.deploy-frontend.outputs.PREVIEW_URL }}" ]; then
            ALLOWED_ORIGIN="${{ steps.deploy-frontend.outputs.PREVIEW_URL }}"
          fi

          # Deploy with volume mount for Firebase secret
          gcloud run deploy $SHORT_NAME-cacao-api \
            --image ${{ vars.GCP_REGION }}-docker.pkg.dev/${{ vars.GCP_PROJECT_ID }}/palmyra-pro-images/cacao-api:$TAG_NAME \
            --region ${{ vars.GCP_REGION }} \
            --project ${{ vars.GCP_PROJECT_ID }} \
            --execution-environment gen2 \
            --min-instances ${{ env.MIN_INSTANCES }} \
            --max-instances 2 \
            --concurrency 1000 \
            --allow-unauthenticated \
            --port 3000 \
            --add-cloudsql-instances $DB_INSTANCE_CONNECTION_NAME \
            --set-env-vars "DB_NAME=${{ env.DB_NAME }},DB_HOST=${{ env.DB_HOST }},DB_PORT=${{ vars.DB_PORT }},DB_USER=${{ vars.DB_USER }},DB_SSL_MODE=${{ vars.DB_SSL_MODE }}" \
            --set-env-vars "ALLOWED_ORIGIN=${ALLOWED_ORIGIN},METABASE_SITE_URL=${{ vars.METABASE_SITE_URL }},ENV_SHORT_NAME=${{ env.SHORT_NAME }}" \
            --set-env-vars "FIREBASE_ADMIN_KEY_PATH=/secrets/FIREBASE_SERVICE_ACCOUNT_KEY,FIREBASE_AUTH_SECRET=${{ vars.FIREBASE_AUTH_SECRET }},FE_URL=${ALLOWED_ORIGIN}" \
            --set-secrets "/secrets/FIREBASE_SERVICE_ACCOUNT_KEY=FIREBASE_SERVICE_ACCOUNT_KEY:latest,DB_PASSWORD=${{ env.GCP_DB_PASSWORD_SECRET_NAME }}:latest,METABASE_SECRET_KEY=METABASE_SECRET_KEY:latest" \
            --labels "env_short_name=${{ env.SHORT_NAME }}"

          # Add IAM policy binding for public access
          gcloud run services add-iam-policy-binding $SHORT_NAME-cacao-api \
            --region ${{ vars.GCP_REGION }} \
            --project ${{ vars.GCP_PROJECT_ID }} \
            --member="allUsers" \
            --role="roles/run.invoker"

          echo "✅ Backend deployed successfully to Cloud Run"
          echo "Service URL: ${{ needs.setup.outputs.api-url }}"

      - name: Comment on PR with deployment URLs
        if: github.event_name == 'pull_request'
        uses: marocchino/sticky-pull-request-comment@v2
        with:
          message: |
            ## 🚀 Environment Deployed Successfully

            **Frontend:** ${{ steps.deploy-frontend.outputs.PREVIEW_URL || needs.setup.outputs.fe-url }}
            **Backend API:** ${{ needs.setup.outputs.api-url }}
            **Database:** ${{ needs.setup.outputs.db-name }}

            Environment: `${{ needs.setup.outputs.short-name }}`

      - name: Deployment Summary
        run: |
          echo "🎉 Deployment completed successfully!"
          echo "===============xxx==================="
          echo "Environment: ${{ needs.setup.outputs.short-name }}"
          FRONTEND_URL="${{ steps.deploy-frontend.outputs.PREVIEW_URL }}"
          if [ -z "$FRONTEND_URL" ]; then
            FRONTEND_URL="${{ needs.setup.outputs.fe-url }}"
          fi
          echo "Frontend URL: $FRONTEND_URL"
          echo "Backend API: ${{ needs.setup.outputs.api-url }}"
          echo "Database: ${{ needs.setup.outputs.db-name }}"
          echo "================xxx=================="
